{% extends "base.html" %}

{% block title %}
قائمة النساء العاملات - لجنة الخدمات الاجتماعية
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-venus me-2 text-danger"></i>قائمة النساء العاملات
                    <span class="badge bg-danger ms-2">{{ total_female_workers }}</span>
                </h5>
                <div>
                    <a href="/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>العودة للرئيسية
                    </a>
                    <a href="/workers" class="btn btn-primary">
                        <i class="fas fa-users me-1"></i>جميع العمال
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- شريط البحث -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <form method="GET" class="d-flex">
                            <input type="text" 
                                   class="form-control me-2" 
                                   name="search" 
                                   value="{{ search }}" 
                                   placeholder="البحث في النساء العاملات...">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                            {% if search %}
                            <a href="/female_workers" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-times"></i>
                            </a>
                            {% endif %}
                        </form>
                    </div>
                    <div class="col-md-6 text-end">
                        {% if search %}
                        <small class="text-muted">
                            نتائج البحث عن: "<strong>{{ search }}</strong>"
                        </small>
                        {% endif %}
                    </div>
                </div>

                <!-- الجدول -->
                {% if workers.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم الكامل</th>
                                <th>تاريخ الميلاد</th>
                                <th>العنوان الشخصي</th>
                                <th>المنصب</th>
                                <th>مكان العمل</th>
                                <th>رقم الهاتف</th>
                                <th>الحالة الاجتماعية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for worker in workers.items %}
                            <tr>
                                <td>
                                    {% if worker.photo %}
                                        <img src="{{ url_for('static', filename='uploads/' + worker.photo) }}" 
                                             alt="صورة {{ worker.first_name }} {{ worker.last_name }}" 
                                             class="worker-photo">
                                    {% else %}
                                        <div class="worker-photo bg-light d-flex align-items-center justify-content-center">
                                            <i class="fas fa-user text-muted"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ worker.last_name }} {{ worker.first_name }}</strong>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-venus text-danger me-1"></i>أنثى
                                    </small>
                                </td>
                                <td>{{ worker.birth_date.strftime('%Y-%m-%d') if worker.birth_date }}</td>
                                <td>{{ worker.personal_title or '-' }}</td>
                                <td>{{ worker.position or '-' }}</td>
                                <td>{{ worker.workplace or '-' }}</td>
                                <td>
                                    {% if worker.phone %}
                                        <a href="tel:{{ worker.phone }}" class="text-decoration-none">
                                            <i class="fas fa-phone text-success me-1"></i>{{ worker.phone }}
                                        </a>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if worker.marital_status == 'متزوج' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-heart me-1"></i>متزوجة
                                        </span>
                                    {% elif worker.marital_status == 'عازب' %}
                                        <span class="badge bg-primary">
                                            <i class="fas fa-user me-1"></i>عازبة
                                        </span>
                                    {% elif worker.marital_status == 'مطلق' %}
                                        <span class="badge bg-warning">
                                            <i class="fas fa-user-times me-1"></i>مطلقة
                                        </span>
                                    {% elif worker.marital_status == 'أرمل' %}
                                        <span class="badge bg-dark">
                                            <i class="fas fa-user-minus me-1"></i>أرملة
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('main.worker_detail', id=worker.id) }}" 
                                           class="btn btn-sm btn-outline-primary" 
                                           title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('main.edit_worker', id=worker.id) }}"
                                           class="btn btn-sm btn-outline-warning"
                                           title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- ترقيم الصفحات -->
                {% if workers.pages > 1 %}
                <nav aria-label="ترقيم الصفحات">
                    <ul class="pagination justify-content-center">
                        {% if workers.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.female_workers', page=workers.prev_num, search=search) }}">
                                السابق
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in workers.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != workers.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('main.female_workers', page=page_num, search=search) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if workers.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('main.female_workers', page=workers.next_num, search=search) }}">
                                التالي
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <!-- رسالة عدم وجود نتائج -->
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    {% if search %}
                    <h5 class="text-muted">لا توجد نتائج للبحث</h5>
                    <p class="text-muted">لم يتم العثور على نساء عاملات تطابق البحث "{{ search }}"</p>
                    <a href="/female_workers" class="btn btn-primary">
                        <i class="fas fa-list me-1"></i>عرض جميع النساء العاملات
                    </a>
                    {% else %}
                    <h5 class="text-muted">لا توجد نساء عاملات</h5>
                    <p class="text-muted">لم يتم تسجيل أي امرأة عاملة بعد</p>
                    <a href="/add_worker" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>إضافة عاملة جديدة
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
{% if workers.items %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>إحصائيات سريعة للنساء العاملات
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-users fa-2x text-danger mb-2"></i>
                            <h5>إجمالي النساء</h5>
                            <h3 class="text-danger">{{ total_female_workers }}</h3>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-heart fa-2x text-success mb-2"></i>
                            <h5>المتزوجات</h5>
                            <h3 class="text-success">
                                {{ workers.items | selectattr('marital_status', 'equalto', 'متزوج') | list | length }}
                            </h3>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-user fa-2x text-primary mb-2"></i>
                            <h5>العازبات</h5>
                            <h3 class="text-primary">
                                {{ workers.items | selectattr('marital_status', 'equalto', 'عازب') | list | length }}
                            </h3>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="border rounded p-3">
                            <i class="fas fa-phone fa-2x text-info mb-2"></i>
                            <h5>لديهن هاتف</h5>
                            <h3 class="text-info">
                                {{ workers.items | selectattr('phone') | list | length }}
                            </h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
