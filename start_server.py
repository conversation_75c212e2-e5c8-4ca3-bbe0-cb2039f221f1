#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف بسيط لتشغيل الخادم
"""

try:
    print("بدء تشغيل الخادم...")
    from app import create_app
    print("تم استيراد التطبيق بنجاح")
    
    app = create_app()
    print("تم إنشاء التطبيق بنجاح")
    
    print("تشغيل الخادم على http://localhost:5000")
    app.run(debug=True, host='127.0.0.1', port=5000)
    
except Exception as e:
    print(f"خطأ في تشغيل الخادم: {e}")
    import traceback
    traceback.print_exc()
