from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app
from werkzeug.utils import secure_filename
from app.models import db, <PERSON>, Child
from app.forms import WorkerForm
import os
from datetime import datetime
try:
    from PIL import Image
except ImportError:
    Image = None

main = Blueprint('main', __name__)

def allowed_file(filename):
    """التحقق من نوع الملف المسموح"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def save_picture(form_picture):
    """حفظ الصورة وإرجاع اسم الملف"""
    if form_picture and allowed_file(form_picture.filename):
        # إنشاء اسم ملف فريد
        filename = secure_filename(form_picture.filename)
        name, ext = os.path.splitext(filename)
        filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{name}{ext}"

        # مسار حفظ الملف
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)

        # حفظ الصورة
        try:
            if Image:
                # تصغير الصورة وحفظها
                image = Image.open(form_picture)
                # تصغير الصورة إلى 300x300 بكسل كحد أقصى
                image.thumbnail((300, 300), Image.Resampling.LANCZOS)
                image.save(file_path, optimize=True, quality=85)
            else:
                # حفظ الصورة بدون تصغير إذا لم تكن PIL متوفرة
                form_picture.save(file_path)
            return filename
        except Exception as e:
            flash(f'خطأ في حفظ الصورة: {str(e)}', 'error')
            return None
    return None

@main.route('/')
def index():
    """الصفحة الرئيسية"""
    # إحصائيات
    total_workers = Worker.query.count()
    total_children = Child.query.count()
    married_workers = Worker.query.filter_by(marital_status='متزوج').count()
    
    # آخر العمال المضافين
    recent_workers = Worker.query.order_by(Worker.created_at.desc()).limit(5).all()
    
    return render_template('index.html',
                         total_workers=total_workers,
                         total_children=total_children,
                         married_workers=married_workers,
                         recent_workers=recent_workers)

@main.route('/workers')
def workers_list():
    """قائمة العمال"""
    from .forms import WorkerForm
    form = WorkerForm()  # إنشاء نموذج للحصول على CSRF token

    search = request.args.get('search', '')

    if search:
        workers = Worker.query.filter(
            (Worker.first_name.contains(search)) |
            (Worker.last_name.contains(search)) |
            (Worker.job_title.contains(search)) |
            (Worker.workplace.contains(search))
        ).order_by(Worker.created_at.desc()).all()
    else:
        workers = Worker.query.order_by(Worker.created_at.desc()).all()

    return render_template('workers_list.html', workers=workers, form=form)

@main.route('/worker/<int:id>')
def worker_detail(id):
    """تفاصيل العامل"""
    worker = Worker.query.get_or_404(id)
    return render_template('worker_detail.html', worker=worker)

@main.route('/add_worker', methods=['GET', 'POST'])
def add_worker():
    """إضافة عامل جديد"""
    form = WorkerForm()
    
    if form.validate_on_submit():
        # إنشاء عامل جديد
        worker = Worker(
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            birth_date=form.birth_date.data,
            birth_place=form.birth_place.data,
            job_title=form.job_title.data,
            position=form.position.data,
            workplace=form.workplace.data,
            gender=form.gender.data,
            marital_status=form.marital_status.data
        )
        
        # حفظ الصورة إن وجدت
        if form.photo.data:
            filename = save_picture(form.photo.data)
            if filename:
                worker.photo_filename = filename
        
        # حفظ العامل في قاعدة البيانات
        db.session.add(worker)
        db.session.flush()  # للحصول على ID العامل
        
        # إضافة الأطفال
        for child_form in form.children.data:
            if child_form['name']:  # التأكد من وجود اسم الطفل
                child = Child(
                    worker_id=worker.id,
                    name=child_form['name'],
                    birth_date=child_form['birth_date'],
                    gender=child_form['gender'],
                    education_level=child_form['education_level']
                )
                db.session.add(child)
        
        db.session.commit()
        flash(f'تم إضافة العامل {worker.full_name} بنجاح!', 'success')
        return redirect(url_for('main.worker_detail', id=worker.id))
    
    return render_template('worker_form.html', form=form, worker=None)

@main.route('/edit_worker/<int:id>', methods=['GET', 'POST'])
def edit_worker(id):
    """تعديل بيانات العامل"""
    worker = Worker.query.get_or_404(id)
    form = WorkerForm()

    if request.method == 'GET':
        # ملء بيانات العامل
        form.first_name.data = worker.first_name
        form.last_name.data = worker.last_name
        form.birth_date.data = worker.birth_date
        form.birth_place.data = worker.birth_place
        form.job_title.data = worker.job_title
        form.position.data = worker.position
        form.workplace.data = worker.workplace
        form.gender.data = worker.gender
        form.marital_status.data = worker.marital_status

        # ملء بيانات الأطفال في النموذج
        for child in worker.children:
            child_form = form.children.append_entry()
            child_form.name.data = child.name
            child_form.birth_date.data = child.birth_date
            child_form.gender.data = child.gender
            child_form.education_level.data = child.education_level
    
    if form.validate_on_submit():
        # تحديث بيانات العامل
        worker.first_name = form.first_name.data
        worker.last_name = form.last_name.data
        worker.birth_date = form.birth_date.data
        worker.birth_place = form.birth_place.data
        worker.job_title = form.job_title.data
        worker.position = form.position.data
        worker.workplace = form.workplace.data
        worker.gender = form.gender.data
        worker.marital_status = form.marital_status.data
        worker.updated_at = datetime.utcnow()
        
        # تحديث الصورة إن وجدت
        if form.photo.data:
            # حذف الصورة القديمة
            if worker.photo_filename:
                old_file = os.path.join(current_app.config['UPLOAD_FOLDER'], worker.photo_filename)
                if os.path.exists(old_file):
                    os.remove(old_file)
            
            # حفظ الصورة الجديدة
            filename = save_picture(form.photo.data)
            if filename:
                worker.photo_filename = filename
        
        # حذف الأطفال الحاليين وإضافة الجدد
        Child.query.filter_by(worker_id=worker.id).delete()
        
        for child_form in form.children.data:
            if child_form['name']:  # التأكد من وجود اسم الطفل
                child = Child(
                    worker_id=worker.id,
                    name=child_form['name'],
                    birth_date=child_form['birth_date'],
                    gender=child_form['gender'],
                    education_level=child_form['education_level']
                )
                db.session.add(child)
        
        db.session.commit()
        flash(f'تم تحديث بيانات العامل {worker.full_name} بنجاح!', 'success')
        return redirect(url_for('main.worker_detail', id=worker.id))
    
    return render_template('worker_form.html', form=form, worker=worker)

@main.route('/delete_worker/<int:id>', methods=['POST'])
def delete_worker(id):
    """حذف العامل"""
    worker = Worker.query.get_or_404(id)
    
    # حذف صورة العامل إن وجدت
    if worker.photo_filename:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], worker.photo_filename)
        if os.path.exists(file_path):
            os.remove(file_path)
    
    # حذف العامل (سيتم حذف الأطفال تلقائياً بسبب cascade)
    worker_name = worker.full_name
    db.session.delete(worker)
    db.session.commit()
    
    flash(f'تم حذف العامل {worker_name} بنجاح!', 'success')
    return redirect(url_for('main.workers_list'))
