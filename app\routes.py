from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app
from werkzeug.utils import secure_filename
from app.models import db, <PERSON>, Child
from app.forms import WorkerForm
import os
from datetime import datetime
import pandas as pd
from io import BytesIO
try:
    from PIL import Image
except ImportError:
    Image = None

main = Blueprint('main', __name__)

def allowed_file(filename):
    """التحقق من نوع الملف المسموح"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def save_picture(form_picture):
    """حفظ الصورة وإرجاع اسم الملف"""
    if form_picture and allowed_file(form_picture.filename):
        # إنشاء اسم ملف فريد
        filename = secure_filename(form_picture.filename)
        name, ext = os.path.splitext(filename)
        filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{name}{ext}"

        # مسار حفظ الملف
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)

        # حفظ الصورة
        try:
            if Image:
                # تصغير الصورة وحفظها
                image = Image.open(form_picture)
                # تصغير الصورة إلى 300x300 بكسل كحد أقصى
                image.thumbnail((300, 300), Image.Resampling.LANCZOS)
                image.save(file_path, optimize=True, quality=85)
            else:
                # حفظ الصورة بدون تصغير إذا لم تكن PIL متوفرة
                form_picture.save(file_path)
            return filename
        except Exception as e:
            flash(f'خطأ في حفظ الصورة: {str(e)}', 'error')
            return None
    return None

@main.route('/')
def index():
    """الصفحة الرئيسية"""
    # إحصائيات
    total_workers = Worker.query.count()
    total_children = Child.query.count()
    married_workers = Worker.query.filter_by(marital_status='متزوج').count()
    
    # آخر العمال المضافين
    recent_workers = Worker.query.order_by(Worker.created_at.desc()).limit(5).all()
    
    return render_template('index.html',
                         total_workers=total_workers,
                         total_children=total_children,
                         married_workers=married_workers,
                         recent_workers=recent_workers)

@main.route('/workers')
def workers_list():
    """قائمة العمال"""
    from .forms import WorkerForm
    form = WorkerForm()  # إنشاء نموذج للحصول على CSRF token

    search = request.args.get('search', '')

    if search:
        workers = Worker.query.filter(
            (Worker.first_name.contains(search)) |
            (Worker.last_name.contains(search)) |
            (Worker.personal_address.contains(search)) |
            (Worker.workplace.contains(search))
        ).order_by(Worker.created_at.desc()).all()
    else:
        workers = Worker.query.order_by(Worker.created_at.desc()).all()

    return render_template('workers_list.html', workers=workers, form=form)

@main.route('/worker/<int:id>')
def worker_detail(id):
    """تفاصيل العامل"""
    worker = Worker.query.get_or_404(id)
    return render_template('worker_detail.html', worker=worker)

@main.route('/add_worker', methods=['GET', 'POST'])
def add_worker():
    """إضافة عامل جديد"""
    form = WorkerForm()

    if request.method == 'POST':
        try:
            # إنشاء عامل جديد
            worker = Worker(
                first_name=request.form.get('first_name'),
                last_name=request.form.get('last_name'),
                birth_date=datetime.strptime(request.form.get('birth_date'), '%Y-%m-%d').date(),
                birth_place=request.form.get('birth_place'),
                personal_address=request.form.get('personal_address'),
                phone=request.form.get('phone'),
                position=request.form.get('position'),
                workplace=request.form.get('workplace'),
                gender=request.form.get('gender'),
                marital_status=request.form.get('marital_status')
            )

            # حفظ الصورة إن وجدت
            if 'photo' in request.files and request.files['photo'].filename:
                photo = request.files['photo']
                filename = save_picture(photo)
                if filename:
                    worker.photo_filename = filename

            # حفظ العامل في قاعدة البيانات
            db.session.add(worker)
            db.session.flush()  # للحصول على ID العامل

            # إضافة الأطفال
            child_index = 0
            while f'children-{child_index}-name' in request.form:
                child_name = request.form.get(f'children-{child_index}-name')
                if child_name:  # التأكد من وجود اسم الطفل
                    child = Child(
                        worker_id=worker.id,
                        name=child_name,
                        birth_date=datetime.strptime(request.form.get(f'children-{child_index}-birth_date'), '%Y-%m-%d').date(),
                        gender=request.form.get(f'children-{child_index}-gender'),
                        education_level=request.form.get(f'children-{child_index}-education_level')
                    )
                    db.session.add(child)
                child_index += 1

            db.session.commit()
            flash(f'تم إضافة العامل {worker.full_name} بنجاح!', 'success')
            return redirect(url_for('main.worker_detail', id=worker.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة العامل: {str(e)}', 'error')

    return render_template('worker_form.html', form=form, worker=None)

@main.route('/edit_worker/<int:id>', methods=['GET', 'POST'])
def edit_worker(id):
    """تعديل بيانات العامل"""
    worker = Worker.query.get_or_404(id)
    form = WorkerForm()

    if form.validate_on_submit():
        try:
            # تحديث بيانات العامل
            worker.first_name = form.first_name.data
            worker.last_name = form.last_name.data
            worker.birth_date = form.birth_date.data
            worker.birth_place = form.birth_place.data
            worker.personal_address = form.personal_address.data
            worker.phone = form.phone.data
            worker.position = form.position.data
            worker.workplace = form.workplace.data
            worker.gender = form.gender.data
            worker.marital_status = form.marital_status.data
            worker.updated_at = datetime.now()

            # تحديث الصورة إن وجدت
            photo = form.photo.data
            if photo and photo.filename:
                if allowed_file(photo.filename):
                    # حذف الصورة القديمة
                    if worker.photo_filename:
                        old_file = os.path.join(current_app.config['UPLOAD_FOLDER'], worker.photo_filename)
                        if os.path.exists(old_file):
                            os.remove(old_file)

                    # حفظ الصورة الجديدة
                    filename = save_picture(photo)
                    if filename:
                        worker.photo_filename = filename

            # معالجة بيانات الأطفال من الطلب مباشرة
            Child.query.filter_by(worker_id=worker.id).delete()

            # الحصول على بيانات الأطفال من الطلب
            i = 0
            while f'children-{i}-name' in request.form:
                name = request.form.get(f'children-{i}-name', '').strip()
                if name:  # إذا كان هناك اسم
                    birth_date_str = request.form.get(f'children-{i}-birth_date')
                    gender = request.form.get(f'children-{i}-gender')
                    education_level = request.form.get(f'children-{i}-education_level')

                    if birth_date_str:
                        try:
                            birth_date = datetime.strptime(birth_date_str, '%Y-%m-%d').date()
                            child = Child(
                                worker_id=worker.id,
                                name=name,
                                birth_date=birth_date,
                                gender=gender,
                                education_level=education_level
                            )
                            db.session.add(child)
                        except ValueError:
                            pass  # تجاهل التواريخ غير الصحيحة
                i += 1

            db.session.commit()
            flash(f'تم تحديث بيانات العامل {worker.full_name} بنجاح!', 'success')
            return redirect(url_for('main.worker_detail', id=worker.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث البيانات: {str(e)}', 'error')
            print(f"خطأ في تحديث العامل: {e}")
            import traceback
            traceback.print_exc()

    # ملء النموذج ببيانات العامل الحالية
    if request.method == 'GET':
        form.first_name.data = worker.first_name
        form.last_name.data = worker.last_name
        form.birth_date.data = worker.birth_date
        form.birth_place.data = worker.birth_place
        form.personal_address.data = worker.personal_address
        form.phone.data = worker.phone
        form.position.data = worker.position
        form.workplace.data = worker.workplace
        form.gender.data = worker.gender
        form.marital_status.data = worker.marital_status

    return render_template('edit_worker.html', form=form, worker=worker)

@main.route('/delete_worker/<int:id>', methods=['POST'])
def delete_worker(id):
    """حذف العامل"""
    worker = Worker.query.get_or_404(id)
    
    # حذف صورة العامل إن وجدت
    if worker.photo_filename:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], worker.photo_filename)
        if os.path.exists(file_path):
            os.remove(file_path)
    
    # حذف العامل (سيتم حذف الأطفال تلقائياً بسبب cascade)
    worker_name = worker.full_name
    db.session.delete(worker)
    db.session.commit()
    
    flash(f'تم حذف العامل {worker_name} بنجاح!', 'success')
    return redirect(url_for('main.workers_list'))

@main.route('/import_workers', methods=['GET', 'POST'])
def import_workers():
    """استيراد العمال من ملف Excel"""
    if request.method == 'POST':
        if 'excel_file' not in request.files:
            flash('لم يتم اختيار ملف!', 'error')
            return redirect(request.url)

        file = request.files['excel_file']
        if file.filename == '':
            flash('لم يتم اختيار ملف!', 'error')
            return redirect(request.url)

        if file and allowed_excel_file(file.filename):
            try:
                # قراءة ملف Excel
                df = pd.read_excel(file, engine='openpyxl')

                # التحقق من وجود الأعمدة المطلوبة
                required_columns = ['الاسم', 'اللقب', 'تاريخ_الميلاد', 'مكان_الميلاد',
                                  'الوظيفة', 'المنصب', 'مكان_العمل', 'الجنس', 'الحالة_الاجتماعية']

                missing_columns = [col for col in required_columns if col not in df.columns]
                if missing_columns:
                    flash(f'الأعمدة التالية مفقودة في الملف: {", ".join(missing_columns)}', 'error')
                    return redirect(request.url)

                success_count = 0
                error_count = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # التحقق من البيانات المطلوبة
                        if pd.isna(row['الاسم']) or pd.isna(row['اللقب']):
                            errors.append(f'الصف {index + 2}: الاسم واللقب مطلوبان')
                            error_count += 1
                            continue

                        # تحويل تاريخ الميلاد
                        birth_date = None
                        if not pd.isna(row['تاريخ_الميلاد']):
                            if isinstance(row['تاريخ_الميلاد'], str):
                                try:
                                    birth_date = datetime.strptime(row['تاريخ_الميلاد'], '%Y-%m-%d').date()
                                except ValueError:
                                    try:
                                        birth_date = datetime.strptime(row['تاريخ_الميلاد'], '%d/%m/%Y').date()
                                    except ValueError:
                                        errors.append(f'الصف {index + 2}: تاريخ الميلاد غير صحيح')
                                        error_count += 1
                                        continue
                            else:
                                birth_date = row['تاريخ_الميلاد'].date() if hasattr(row['تاريخ_الميلاد'], 'date') else row['تاريخ_الميلاد']

                        if not birth_date:
                            errors.append(f'الصف {index + 2}: تاريخ الميلاد مطلوب')
                            error_count += 1
                            continue

                        # دالة مساعدة لتحويل القيم بأمان
                        def safe_str(value):
                            if pd.isna(value):
                                return ''
                            return str(value).strip()

                        # إنشاء العامل
                        worker = Worker(
                            first_name=safe_str(row['الاسم']),
                            last_name=safe_str(row['اللقب']),
                            birth_date=birth_date,
                            birth_place=safe_str(row['مكان_الميلاد']),
                            personal_address=safe_str(row['العنوان_الشخصي']),
                            phone=safe_str(row['الهاتف']),
                            position=safe_str(row['المنصب']),
                            workplace=safe_str(row['مكان_العمل']),
                            gender=safe_str(row['الجنس']) or 'ذكر',
                            marital_status=safe_str(row['الحالة_الاجتماعية']) or 'عازب'
                        )

                        db.session.add(worker)
                        success_count += 1

                    except Exception as e:
                        errors.append(f'الصف {index + 2}: خطأ في المعالجة - {str(e)}')
                        error_count += 1
                        continue

                # حفظ البيانات
                if success_count > 0:
                    db.session.commit()
                    flash(f'تم استيراد {success_count} عامل بنجاح!', 'success')
                else:
                    db.session.rollback()

                if error_count > 0:
                    flash(f'فشل في استيراد {error_count} صف. الأخطاء: {"; ".join(errors[:5])}', 'warning')

                return redirect(url_for('main.workers_list'))

            except Exception as e:
                db.session.rollback()
                flash(f'خطأ في قراءة الملف: {str(e)}', 'error')
                return redirect(request.url)
        else:
            flash('نوع الملف غير مدعوم! يرجى استخدام ملفات Excel (.xlsx, .xls)', 'error')

    return render_template('import_workers.html')

def allowed_excel_file(filename):
    """التحقق من نوع ملف Excel"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ['xlsx', 'xls']

@main.route('/download_template')
def download_template():
    """تحميل نموذج Excel للعمال"""
    from flask import send_file, make_response

    # إنشاء DataFrame مع البيانات النموذجية
    data = {
        'الاسم': ['أحمد', 'فاطمة', 'محمد'],
        'اللقب': ['بن علي', 'بن محمد', 'قاسمي'],
        'تاريخ_الميلاد': ['1985-03-15', '1990-07-22', '1988-12-10'],
        'مكان_الميلاد': ['الجلفة', 'حاسي بحبح', 'عين الإبل'],
        'الوظيفة': ['مهندس', 'معلمة', 'طبيب'],
        'المنصب': ['مهندس أول', 'معلمة رئيسية', 'طبيب عام'],
        'مكان_العمل': ['مديرية التربية', 'ابتدائية النور', 'مستشفى الجلفة'],
        'الجنس': ['ذكر', 'أنثى', 'ذكر'],
        'الحالة_الاجتماعية': ['متزوج', 'متزوج', 'عازب']
    }

    df = pd.DataFrame(data)

    # إنشاء ملف Excel في الذاكرة
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='العمال', index=False)

        # تنسيق الجدول
        workbook = writer.book
        worksheet = writer.sheets['العمال']

        # تعديل عرض الأعمدة
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    output.seek(0)

    # إرسال الملف
    response = make_response(output.read())
    response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    response.headers['Content-Disposition'] = 'attachment; filename=نموذج_العمال.xlsx'

    return response

@main.route('/logo_settings', methods=['GET', 'POST'])
def logo_settings():
    """إعدادات الشعار والخلفية المائية"""
    if request.method == 'POST':
        if 'logo' in request.files and request.files['logo'].filename:
            logo_file = request.files['logo']
            if logo_file and allowed_file(logo_file.filename):
                # حذف الشعار القديم إن وجد
                old_logo_path = os.path.join(current_app.config['UPLOAD_FOLDER'], '..', 'images', 'logo.png')
                if os.path.exists(old_logo_path):
                    os.remove(old_logo_path)

                # حفظ الشعار الجديد
                logo_path = os.path.join(current_app.config['UPLOAD_FOLDER'], '..', 'images', 'logo.png')

                try:
                    if Image:
                        # تصغير الشعار وحفظه
                        image = Image.open(logo_file)
                        # تحويل إلى RGBA للشفافية
                        if image.mode != 'RGBA':
                            image = image.convert('RGBA')
                        # تصغير الشعار إلى 500x500 بكسل كحد أقصى
                        image.thumbnail((500, 500), Image.Resampling.LANCZOS)
                        image.save(logo_path, 'PNG', optimize=True)
                    else:
                        # حفظ الشعار بدون تصغير إذا لم تكن PIL متوفرة
                        logo_file.save(logo_path)

                    flash('تم تحديث الشعار بنجاح!', 'success')
                except Exception as e:
                    flash(f'خطأ في حفظ الشعار: {str(e)}', 'error')
            else:
                flash('نوع الملف غير مدعوم! يرجى استخدام صور PNG, JPG, أو GIF', 'error')

        return redirect(url_for('main.logo_settings'))

    # التحقق من وجود الشعار
    logo_path = os.path.join(current_app.config['UPLOAD_FOLDER'], '..', 'images', 'logo.png')
    logo_exists = os.path.exists(logo_path)

    return render_template('logo_settings.html', logo_exists=logo_exists)
