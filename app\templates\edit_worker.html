{% extends "base.html" %}

{% block title %}
تعديل العامل - لجنة الخدمات الاجتماعية
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    تعديل بيانات العامل
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="row">
                        <!-- البيانات الشخصية -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user me-1"></i>البيانات الشخصية
                            </h6>
                            
                            <div class="mb-3">
                                <label class="form-label" for="first_name">الاسم</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="{{ worker.first_name }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label" for="last_name">اللقب</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="{{ worker.last_name }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label" for="birth_date">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="birth_date" name="birth_date" 
                                       value="{{ worker.birth_date.strftime('%Y-%m-%d') if worker.birth_date else '' }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label" for="birth_place">مكان الميلاد</label>
                                <input type="text" class="form-control" id="birth_place" name="birth_place" 
                                       value="{{ worker.birth_place }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label" for="gender">الجنس</label>
                                <select class="form-select" id="gender" name="gender" required>
                                    <option value="ذكر" {% if worker.gender == 'ذكر' %}selected{% endif %}>ذكر</option>
                                    <option value="أنثى" {% if worker.gender == 'أنثى' %}selected{% endif %}>أنثى</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label" for="marital_status">الحالة الاجتماعية</label>
                                <select class="form-select" id="marital_status" name="marital_status" required>
                                    <option value="متزوج" {% if worker.marital_status == 'متزوج' %}selected{% endif %}>متزوج</option>
                                    <option value="عازب" {% if worker.marital_status == 'عازب' %}selected{% endif %}>عازب</option>
                                    <option value="مطلق" {% if worker.marital_status == 'مطلق' %}selected{% endif %}>مطلق</option>
                                    <option value="أرمل" {% if worker.marital_status == 'أرمل' %}selected{% endif %}>أرمل</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- بيانات العمل -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-briefcase me-1"></i>بيانات العمل
                            </h6>
                            
                            <div class="mb-3">
                                <label class="form-label" for="job_title">الوظيفة</label>
                                <input type="text" class="form-control" id="job_title" name="job_title" 
                                       value="{{ worker.job_title }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label" for="position">المنصب</label>
                                <input type="text" class="form-control" id="position" name="position" 
                                       value="{{ worker.position }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label" for="workplace">مكان العمل</label>
                                <input type="text" class="form-control" id="workplace" name="workplace" 
                                       value="{{ worker.workplace }}" required>
                            </div>
                            
                            <!-- صورة العامل -->
                            <div class="mb-3">
                                <label class="form-label" for="photo">صورة العامل</label>
                                <input type="file" class="form-control" id="photo" name="photo" accept="image/*">
                                {% if worker.photo_filename %}
                                    <div class="mt-2">
                                        <img src="{{ url_for('static', filename='uploads/' + worker.photo_filename) }}" 
                                             alt="صورة العامل" class="img-thumbnail" style="max-width: 150px;">
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- بيانات الأطفال -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-child me-1"></i>بيانات الأطفال
                                <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="addChild()">
                                    <i class="fas fa-plus"></i> إضافة طفل
                                </button>
                            </h6>
                            
                            <div id="children-container">
                                {% for child in worker.children %}
                                    <div class="child-form border p-3 mb-3 rounded">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <label class="form-label">اسم الطفل</label>
                                                <input type="text" name="children-{{ loop.index0 }}-name" 
                                                       class="form-control" value="{{ child.name }}" required>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">تاريخ الميلاد</label>
                                                <input type="date" name="children-{{ loop.index0 }}-birth_date" 
                                                       class="form-control" value="{{ child.birth_date.strftime('%Y-%m-%d') if child.birth_date else '' }}" required>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">الجنس</label>
                                                <select name="children-{{ loop.index0 }}-gender" class="form-select" required>
                                                    <option value="">اختر...</option>
                                                    <option value="ذكر" {% if child.gender == 'ذكر' %}selected{% endif %}>ذكر</option>
                                                    <option value="أنثى" {% if child.gender == 'أنثى' %}selected{% endif %}>أنثى</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">المستوى الدراسي</label>
                                                <select name="children-{{ loop.index0 }}-education_level" class="form-select" required>
                                                    <option value="">اختر...</option>
                                                    <option value="ابتدائي" {% if child.education_level == 'ابتدائي' %}selected{% endif %}>ابتدائي</option>
                                                    <option value="متوسط" {% if child.education_level == 'متوسط' %}selected{% endif %}>متوسط</option>
                                                    <option value="ثانوي" {% if child.education_level == 'ثانوي' %}selected{% endif %}>ثانوي</option>
                                                    <option value="جامعي" {% if child.education_level == 'جامعي' %}selected{% endif %}>جامعي</option>
                                                </select>
                                            </div>
                                            <div class="col-md-1 d-flex align-items-end">
                                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeChild(this)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{{ url_for('main.workers_list') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>حفظ
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let childIndex = {{ worker.children|length }};

function addChild() {
    const container = document.getElementById('children-container');
    const childForm = `
        <div class="child-form border p-3 mb-3 rounded">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">اسم الطفل</label>
                    <input type="text" name="children-${childIndex}-name" class="form-control" required>
                </div>
                <div class="col-md-3">
                    <label class="form-label">تاريخ الميلاد</label>
                    <input type="date" name="children-${childIndex}-birth_date" class="form-control" required>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الجنس</label>
                    <select name="children-${childIndex}-gender" class="form-select" required>
                        <option value="">اختر...</option>
                        <option value="ذكر">ذكر</option>
                        <option value="أنثى">أنثى</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">المستوى الدراسي</label>
                    <select name="children-${childIndex}-education_level" class="form-select" required>
                        <option value="">اختر...</option>
                        <option value="ابتدائي">ابتدائي</option>
                        <option value="متوسط">متوسط</option>
                        <option value="ثانوي">ثانوي</option>
                        <option value="جامعي">جامعي</option>
                    </select>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeChild(this)">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', childForm);
    childIndex++;
}

function removeChild(button) {
    button.closest('.child-form').remove();
}
</script>
{% endblock %}
