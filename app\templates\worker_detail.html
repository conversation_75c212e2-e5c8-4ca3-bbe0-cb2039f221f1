{% extends "base.html" %}

{% block title %}{{ worker.full_name }} - تفاصيل العامل{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>تفاصيل العامل: {{ worker.full_name }}
                </h5>
                <div>
                    <a href="{{ url_for('main.edit_worker', id=worker.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>تعديل
                    </a>
                    <a href="{{ url_for('main.workers_list') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- الصورة والمعلومات الأساسية -->
                    <div class="col-md-4 text-center">
                        {% if worker.photo_filename %}
                            <img src="{{ url_for('static', filename='uploads/' + worker.photo_filename) }}" 
                                 alt="صورة {{ worker.full_name }}" 
                                 class="img-fluid rounded-circle mb-3" 
                                 style="max-width: 200px; max-height: 200px; object-fit: cover;">
                        {% else %}
                            <div class="bg-secondary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" 
                                 style="width: 200px; height: 200px;">
                                <i class="fas fa-user fa-5x text-white"></i>
                            </div>
                        {% endif %}
                        
                        <h4 class="text-primary">{{ worker.full_name }}</h4>
                        <p class="text-muted">{{ worker.job_title }}</p>
                        
                        <div class="mt-3">
                            <span class="badge bg-info fs-6">{{ worker.gender }}</span>
                            <span class="badge bg-success fs-6">{{ worker.marital_status }}</span>
                        </div>
                    </div>
                    
                    <!-- البيانات التفصيلية -->
                    <div class="col-md-8">
                        <div class="row">
                            <!-- البيانات الشخصية -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-user me-1"></i>البيانات الشخصية
                                </h6>
                                
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">الاسم:</td>
                                        <td>{{ worker.first_name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">اللقب:</td>
                                        <td>{{ worker.last_name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">تاريخ الميلاد:</td>
                                        <td>{{ worker.birth_date.strftime('%Y-%m-%d') }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">مكان الميلاد:</td>
                                        <td>{{ worker.birth_place }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">الجنس:</td>
                                        <td>{{ worker.gender }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">الحالة الاجتماعية:</td>
                                        <td>{{ worker.marital_status }}</td>
                                    </tr>
                                </table>
                            </div>
                            
                            <!-- بيانات العمل -->
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-briefcase me-1"></i>بيانات العمل
                                </h6>
                                
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">الوظيفة:</td>
                                        <td>{{ worker.job_title }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">المنصب:</td>
                                        <td>{{ worker.position }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">مكان العمل:</td>
                                        <td>{{ worker.workplace }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">تاريخ الإضافة:</td>
                                        <td>{{ worker.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    </tr>
                                    {% if worker.updated_at != worker.created_at %}
                                    <tr>
                                        <td class="fw-bold">آخر تحديث:</td>
                                        <td>{{ worker.updated_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    </tr>
                                    {% endif %}
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- بيانات الأطفال -->
                {% if worker.children %}
                <div class="row mt-4">
                    <div class="col-12">
                        <h6 class="text-primary mb-3">
                            <i class="fas fa-child me-1"></i>بيانات الأطفال ({{ worker.children_count }})
                        </h6>
                        
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الطفل</th>
                                        <th>تاريخ الميلاد</th>
                                        <th>العمر</th>
                                        <th>الجنس</th>
                                        <th>المستوى الدراسي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for child in worker.children %}
                                    <tr>
                                        <td>{{ loop.index }}</td>
                                        <td>{{ child.name }}</td>
                                        <td>{{ child.birth_date.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            {% set birth_year = child.birth_date.year %}
                                            {% set current_year = 2025 %}
                                            {{ current_year - birth_year }} سنة
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 'primary' if child.gender == 'ذكر' else 'danger' }}">
                                                {{ child.gender }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ child.education_level }}</span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد بيانات أطفال مسجلة لهذا العامل
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- إحصائيات سريعة -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-chart-bar me-1"></i>إحصائيات سريعة
                                </h6>
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="border-end">
                                            <h4 class="text-primary">{{ worker.children_count }}</h4>
                                            <small class="text-muted">عدد الأطفال</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="border-end">
                                            <h4 class="text-success">
                                                {{ worker.children | selectattr('gender', 'equalto', 'ذكر') | list | length }}
                                            </h4>
                                            <small class="text-muted">الذكور</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="border-end">
                                            <h4 class="text-danger">
                                                {{ worker.children | selectattr('gender', 'equalto', 'أنثى') | list | length }}
                                            </h4>
                                            <small class="text-muted">الإناث</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <h4 class="text-info">
                                            {{ worker.children | selectattr('education_level', 'equalto', 'جامعي') | list | length }}
                                        </h4>
                                        <small class="text-muted">في الجامعة</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
