/* تنسيقات إضافية للتطبيق */

/* الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
    direction: rtl;
    text-align: right;
}

/* تنسيق الشريط العلوي */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

/* تنسيق البطاقات */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* تنسيق الأزرار */
.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    font-weight: 600;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* تنسيق الجداول */
.table th {
    background-color: #e9ecef;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

/* تنسيق صور العمال */
.worker-photo {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.worker-photo-large {
    width: 200px;
    height: 200px;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* تنسيق النماذج */
.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #495057;
}

.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* تنسيق نماذج الأطفال */
.child-form {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.child-form:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تنسيق الشارات */
.badge {
    font-size: 0.875em;
    padding: 0.5em 0.75em;
}

/* تنسيق التنبيهات */
.alert {
    border-radius: 0.5rem;
    border: none;
    font-weight: 500;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* تنسيق الصفحة الرئيسية */
.jumbotron {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 1rem;
    padding: 3rem 2rem;
}

/* تنسيق الإحصائيات */
.stats-card {
    text-align: center;
    padding: 2rem 1rem;
    border-radius: 1rem;
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0.5rem 0;
}

/* تنسيق أيقونات الإحصائيات */
.stats-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

/* تنسيق تفاصيل العامل */
.worker-detail-photo {
    max-width: 250px;
    max-height: 250px;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid #fff;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

/* تنسيق جدول الأطفال */
.children-table {
    background-color: #fff;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تنسيق البحث */
.search-box {
    border-radius: 2rem;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.search-box:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* تنسيق الأزرار المجمعة */
.btn-group .btn {
    margin: 0 2px;
}

/* تنسيق المودال */
.modal-content {
    border-radius: 1rem;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 1rem 1rem 0 0;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 1rem 1rem;
}

/* تنسيق رسائل الخطأ */
.text-danger {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* تنسيق الحاويات */
.container {
    max-width: 1200px;
}

/* تنسيق الفواصل */
hr {
    border: none;
    height: 1px;
    background: linear-gradient(to right, transparent, #dee2e6, transparent);
    margin: 2rem 0;
}

/* تنسيق الروابط */
a {
    text-decoration: none;
    transition: all 0.3s ease;
}

a:hover {
    text-decoration: none;
}

/* تنسيق الحقول المطلوبة */
.required::after {
    content: " *";
    color: #dc3545;
}

/* تنسيق الحالة الفارغة */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* تنسيق الطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .btn {
        display: none;
    }
}

/* تنسيق الشاشات الصغيرة */
@media (max-width: 768px) {
    .worker-photo {
        width: 60px;
        height: 60px;
    }
    
    .worker-detail-photo {
        max-width: 150px;
        max-height: 150px;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .jumbotron {
        padding: 2rem 1rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
}
