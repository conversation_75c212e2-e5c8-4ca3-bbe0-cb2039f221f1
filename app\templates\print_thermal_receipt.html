<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وصل استلام خاص بالحمام المعدني - {{ receipt.receipt_number }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

        body {
            font-family: '<PERSON><PERSON>', serif;
            background-color: white;
            margin: 0;
            padding: 8mm 2mm 5mm 2mm;
            direction: rtl;
            font-size: 16px;
            line-height: 1.2;
        }

        .page-container {
            width: 196mm;
            margin: 0 auto;
        }

        .receipt-container {
            width: 100%;
            background: white;
            border: 3px solid #000;
            padding: 6mm 8mm;
            margin-bottom: 3mm;
            position: relative;
            page-break-inside: avoid;
            min-height: 90mm;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #000;
            padding-bottom: 3mm;
            margin-bottom: 4mm;
        }

        .header-center {
            flex: 1;
            text-align: center;
            margin: 0 5mm;
        }

        .logo {
            width: 20mm;
            height: 20mm;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .logo-placeholder {
            width: 20mm;
            height: 20mm;
            border: 2px dashed #ccc;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            color: #999;
            text-align: center;
        }

        .header h1 {
            font-size: 18px;
            margin: 1mm 0;
            font-weight: bold;
        }

        .header h2 {
            font-size: 16px;
            margin: 1mm 0;
            font-weight: bold;
        }

        .header h3 {
            font-size: 14px;
            margin: 0.5mm 0;
        }

        .receipt-title {
            text-align: center;
            margin: 3mm 0;
            font-size: 18px;
            font-weight: bold;
        }

        .receipt-subtitle {
            text-align: center;
            margin: 2mm 0;
            font-size: 16px;
        }

        .receipt-number {
            text-align: right;
            margin: 2mm 0;
            font-size: 16px;
            font-weight: bold;
        }

        .info-section {
            margin: 3mm 0;
        }

        .info-row {
            display: flex;
            margin: 2mm 0;
            align-items: center;
        }

        .info-label {
            font-weight: bold;
            margin-left: 3mm;
            min-width: 25mm;
            font-size: 16px;
        }

        .info-value {
            border-bottom: 1px solid #000;
            flex: 1;
            padding: 1mm 2mm;
            min-height: 4mm;
            font-size: 16px;
        }

        .signature-section {
            margin-top: 5mm;
            text-align: left;
        }

        .date-location {
            margin: 3mm 0;
            text-align: left;
            font-size: 16px;
        }

        .signature-title {
            margin-top: 3mm;
            font-weight: bold;
            font-size: 16px;
        }

        @media print {
            body {
                background-color: white;
                padding: 8mm 2mm 5mm 2mm;
                margin: 0;
            }

            .page-container {
                width: 100%;
                margin: 0;
            }

            .receipt-container {
                border: 3px solid #000;
                margin-bottom: 3mm;
                page-break-inside: avoid;
                padding: 6mm 8mm;
            }

            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- الوصل الأول -->
        <div class="receipt-container">
            <!-- Header -->
            <div class="header">
                <!-- الشعار الأيسر -->
                <div class="logo">
                    {% if logo_exists %}
                        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="شعار الولاية">
                    {% else %}
                        <div class="logo-placeholder">شعار</div>
                    {% endif %}
                </div>

                <!-- العنوان الرئيسي -->
                <div class="header-center">
                    <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
                    <h2>ولاية الجلفة</h2>
                    <h3>مديرية الإدارة المحلية</h3>
                    <h3>لجنة الخدمات الاجتماعية لمقر الولاية و الدوائر</h3>
                </div>

                <!-- الشعار الأيمن -->
                <div class="logo">
                    {% if logo_exists %}
                        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="شعار الولاية">
                    {% else %}
                        <div class="logo-placeholder">شعار</div>
                    {% endif %}
                </div>
            </div>

            <!-- رقم الوصل -->
            <div class="receipt-number">
                الرقم: {{ receipt.receipt_number }}
            </div>

            <!-- عنوان الوصل -->
            <div class="receipt-title">
                وصل استلام خاص بالحمام المعدني
            </div>

            <div class="receipt-subtitle">
                {{ receipt.treatment_location }} المبلغ ({{ "{:,.0f}".format(receipt.worker_payment) }} دج)
            </div>

            <!-- معلومات الموظف -->
            <div class="info-section">
                <div class="info-row">
                    <span class="info-label">الاسم و اللقب:</span>
                    <div class="info-value">{{ receipt.worker.family_name }} {{ receipt.worker.given_name if receipt.worker else '' }}</div>
                </div>

                <div class="info-row">
                    <span class="info-label">التعيين:</span>
                    <div class="info-value">{{ receipt.worker.personal_title if receipt.worker else '' }}</div>
                </div>

                <div class="info-row">
                    <span class="info-label">الفوج:</span>
                    <div class="info-value">{{ receipt.worker.category if receipt.worker else '' }}</div>
                </div>

                <div class="info-row">
                    <span class="info-label">رقم الهاتف:</span>
                    <div class="info-value">{{ receipt.worker.phone_number if receipt.worker else '' }}</div>
                </div>
            </div>

            <!-- التاريخ والمكان -->
            <div class="date-location">
                الجلفة في {{ receipt.issue_date.strftime('%Y/%m/%d') if receipt.issue_date else '____/__/__' }}
            </div>

            <!-- التوقيع -->
            <div class="signature-section">
                <div class="signature-title">نائب رئيس لجنة الخدمات</div>
            </div>
        </div>

        <!-- الوصل الثاني (نسخة مطابقة) -->
        <div class="receipt-container">
            <!-- Header -->
            <div class="header">
                <!-- الشعار الأيسر -->
                <div class="logo">
                    {% if logo_exists %}
                        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="شعار الولاية">
                    {% else %}
                        <div class="logo-placeholder">شعار</div>
                    {% endif %}
                </div>

                <!-- العنوان الرئيسي -->
                <div class="header-center">
                    <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
                    <h2>ولاية الجلفة</h2>
                    <h3>مديرية الإدارة المحلية</h3>
                    <h3>لجنة الخدمات الاجتماعية لمقر الولاية و الدوائر</h3>
                </div>

                <!-- الشعار الأيمن -->
                <div class="logo">
                    {% if logo_exists %}
                        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="شعار الولاية">
                    {% else %}
                        <div class="logo-placeholder">شعار</div>
                    {% endif %}
                </div>
            </div>

            <!-- رقم الوصل -->
            <div class="receipt-number">
                الرقم: {{ receipt.receipt_number }}
            </div>

            <!-- عنوان الوصل -->
            <div class="receipt-title">
                وصل استلام خاص بالحمام المعدني
            </div>

            <div class="receipt-subtitle">
                {{ receipt.treatment_location }} المبلغ ({{ "{:,.0f}".format(receipt.worker_payment) }} دج)
            </div>

            <!-- معلومات الموظف -->
            <div class="info-section">
                <div class="info-row">
                    <span class="info-label">الاسم و اللقب:</span>
                    <div class="info-value">{{ receipt.worker.family_name }} {{ receipt.worker.given_name if receipt.worker else '' }}</div>
                </div>

                <div class="info-row">
                    <span class="info-label">التعيين:</span>
                    <div class="info-value">{{ receipt.worker.personal_title if receipt.worker else '' }}</div>
                </div>

                <div class="info-row">
                    <span class="info-label">الفوج:</span>
                    <div class="info-value">{{ receipt.worker.category if receipt.worker else '' }}</div>
                </div>

                <div class="info-row">
                    <span class="info-label">رقم الهاتف:</span>
                    <div class="info-value">{{ receipt.worker.phone_number if receipt.worker else '' }}</div>
                </div>
            </div>

            <!-- التاريخ والمكان -->
            <div class="date-location">
                الجلفة في {{ receipt.issue_date.strftime('%Y/%m/%d') if receipt.issue_date else '____/__/__' }}
            </div>

            <!-- التوقيع -->
            <div class="signature-section">
                <div class="signature-title">نائب رئيس لجنة الخدمات</div>
            </div>
        </div>
    </div>

    <!-- أزرار الطباعة والعودة -->
    <div style="text-align: center; margin-top: 20px;" class="no-print">
        <button onclick="window.print()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px; cursor: pointer;">
            طباعة الوصل
        </button>
        <a href="{{ url_for('main.thermal_receipts_list') }}" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;">
            العودة للقائمة
        </a>
    </div>
</body>
</html>
