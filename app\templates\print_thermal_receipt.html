<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وصل استلام ذات للحمام المعدني - {{ receipt.receipt_number }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: white;
            color: black;
        }
        
        .receipt-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #0d6efd;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
        }
        
        .receipt-title {
            background-color: #f8f9fa;
            padding: 15px;
            border: 2px solid #0d6efd;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        
        .receipt-number {
            background-color: #0d6efd;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .info-table {
            margin: 20px 0;
        }
        
        .info-table td {
            padding: 8px 15px;
            border: 1px solid #dee2e6;
        }
        
        .info-table .label {
            background-color: #e9ecef;
            font-weight: bold;
            width: 30%;
        }
        
        .amount-section {
            background-color: #f8f9fa;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            text-align: center;
            width: 200px;
        }
        
        .signature-line {
            border-bottom: 2px solid #000;
            margin-bottom: 10px;
            height: 60px;
        }
        
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            opacity: 0.1;
            font-size: 4rem;
            font-weight: bold;
            color: #0d6efd;
            z-index: -1;
            pointer-events: none;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 0;
            }
            
            .receipt-container {
                max-width: none;
                margin: 0;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Watermark -->
    <div class="watermark">لجنة الخدمات الاجتماعية</div>
    
    <div class="receipt-container">
        <!-- Header -->
        <div class="header">
            <div class="d-flex justify-content-between align-items-center">
                <!-- الشعار الأيسر -->
                <div class="logo">
                    {% if logo_exists %}
                    <img src="{{ url_for('static', filename='images/logo.png') }}"
                         alt="شعار اللجنة" class="img-fluid">
                    {% else %}
                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-users fa-2x text-white"></i>
                    </div>
                    {% endif %}
                </div>

                <!-- العنوان الرئيسي مع رقم الوصل -->
                <div class="flex-grow-1 text-center">
                    <!-- رقم الوصل فوق العنوان -->
                    <div class="receipt-number">
                        الرقم: {{ receipt.receipt_number }}
                    </div>
                    <h4 class="mb-1">الجمهورية الجزائرية الديمقراطية الشعبية</h4>
                    <h5 class="mb-1">ولاية الجلفة - مديرية الإدارة المحلية</h5>
                    <h6 class="mb-0">لجنة الخدمات الاجتماعية لموظفي الإدارة والولاية</h6>
                </div>

                <!-- الشعار الأيمن -->
                <div class="logo">
                    {% if logo_exists %}
                    <img src="{{ url_for('static', filename='images/logo.png') }}"
                         alt="شعار اللجنة" class="img-fluid">
                    {% else %}
                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-users fa-2x text-white"></i>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- عنوان الوصل -->
        <div class="receipt-title">
            <h3 class="mb-2">وصل استلام ذات للحمام المعدني</h3>
            <h4 class="text-primary">حمام الشارف المبلغ: ({{ "{:,.0f}".format(receipt.total_amount) }} د ج)</h4>
        </div>
        
        <!-- معلومات العامل -->
        <table class="table info-table">
            <tr>
                <td class="label">الاسم والنسب:</td>
                <td>{{ receipt.worker.full_name }}</td>
                <td class="label">رقم الهاتف:</td>
                <td>{{ receipt.worker.phone or 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label">التعيين:</td>
                <td>{{ receipt.worker.position or 'غير محدد' }}</td>
                <td class="label">بلدية عين معبد:</td>
                <td>{{ receipt.worker.workplace or 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label">الفئة:</td>
                <td>{{ receipt.worker.position or 'غير محدد' }}</td>
                <td class="label">رقم الهاتف:</td>
                <td>{{ receipt.worker.phone or 'غير محدد' }}</td>
            </tr>
        </table>
        
        <!-- تفاصيل المبالغ -->
        <div class="amount-section">
            <h5 class="text-center mb-3">تفاصيل المبالغ</h5>
            <div class="row">
                <div class="col-md-4 text-center">
                    <h6>المبلغ الإجمالي</h6>
                    <h4 class="text-info">{{ "{:,.0f}".format(receipt.total_amount) }} د.ج</h4>
                </div>
                <div class="col-md-4 text-center">
                    <h6>مساهمة اللجنة</h6>
                    <h4 class="text-success">{{ "{:,.0f}".format(receipt.committee_contribution) }} د.ج</h4>
                </div>
                <div class="col-md-4 text-center">
                    <h6>دفع العامل</h6>
                    <h4 class="text-warning">{{ "{:,.0f}".format(receipt.worker_payment) }} د.ج</h4>
                </div>
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <table class="table info-table">
            <tr>
                <td class="label">مكان العلاج:</td>
                <td colspan="3">{{ receipt.treatment_location }}</td>
            </tr>

            {% if receipt.notes %}
            <tr>
                <td class="label">ملاحظات:</td>
                <td colspan="3">{{ receipt.notes }}</td>
            </tr>
            {% endif %}
        </table>
        
        <!-- التوقيعات -->
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-line"></div>
                <strong>نائب رئيس لجنة الخدمات</strong><br>
                <small>نائب بوحفص عبد الحميد الاجتماعية</small><br>
                <small>بن عطية عصمت</small>
            </div>
            
            <div class="signature-box">
                <div class="signature-line"></div>
                <strong>توقيع المستفيد</strong>
            </div>
        </div>
        
        <!-- أزرار الطباعة -->
        <div class="text-center mt-4 no-print">
            <button onclick="window.print()" class="btn btn-primary me-2">
                <i class="fas fa-print me-1"></i>طباعة
            </button>
            <button onclick="window.close()" class="btn btn-secondary">
                <i class="fas fa-times me-1"></i>إغلاق
            </button>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>

    <script>
        // تعيين تاريخ الطباعة
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const dateString = today.getFullYear() + '/' +
                              String(today.getMonth() + 1).padStart(2, '0') + '/' +
                              String(today.getDate()).padStart(2, '0');
            document.getElementById('printDate').textContent = dateString;
        });

        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
