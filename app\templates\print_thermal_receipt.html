<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وصل استلام ذات للحمام المعدني - {{ receipt.receipt_number }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
        
        body {
            font-family: '<PERSON><PERSON>', serif;
            background-color: white;
            margin: 0;
            padding: 10mm;
            direction: rtl;
            font-size: 12px;
        }
        
        .page-container {
            width: 190mm;
            margin: 0 auto;
        }
        
        .receipt-container {
            width: 100%;
            background: white;
            border: 2px solid #000;
            padding: 10mm;
            margin-bottom: 5mm;
            position: relative;
            page-break-inside: avoid;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            border-bottom: 2px solid #000;
            padding-bottom: 8mm;
            margin-bottom: 5mm;
        }
        
        .logo {
            width: 20mm;
            height: 20mm;
            background: #ccc;
            border-radius: 50%;
            flex-shrink: 0;
        }
        
        .header-center {
            flex: 1;
            text-align: center;
            margin: 0 5mm;
        }
        
        .receipt-number {
            background-color: #0d6efd;
            color: white;
            padding: 3mm 8mm;
            border-radius: 3mm;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 3mm;
            font-size: 14px;
        }
        
        .header h1 {
            font-size: 16px;
            margin: 1mm 0;
            font-weight: bold;
        }
        
        .header h2 {
            font-size: 14px;
            margin: 1mm 0;
        }
        
        .header h3 {
            font-size: 12px;
            margin: 1mm 0;
        }
        
        .receipt-title {
            background: #007bff;
            color: white;
            padding: 5mm;
            border-radius: 5mm;
            text-align: center;
            margin: 5mm 0;
            border: 2px solid #0056b3;
        }
        
        .receipt-title h2 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
        }
        
        .receipt-title .subtitle {
            font-size: 14px;
            margin-top: 2mm;
        }
        
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 3mm 0;
            font-size: 11px;
        }
        
        .info-table td {
            padding: 2mm 3mm;
            border: 1px solid #000;
            vertical-align: middle;
        }
        
        .info-table .label {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
            width: 25%;
        }
        
        .amount-section {
            background: #28a745;
            color: white;
            padding: 5mm;
            border-radius: 5mm;
            margin: 5mm 0;
            text-align: center;
            border: 2px solid #1e7e34;
        }
        
        .amount-section h3 {
            margin: 0 0 3mm 0;
            font-size: 16px;
        }
        
        .amount-grid {
            display: flex;
            justify-content: space-between;
            margin-top: 3mm;
        }
        
        .amount-item {
            text-align: center;
            flex: 1;
        }
        
        .amount-label {
            font-weight: bold;
            font-size: 11px;
            margin-bottom: 2mm;
            display: block;
        }
        
        .amount-value {
            font-size: 16px;
            font-weight: bold;
        }
        
        .total-amount .amount-value {
            color: #87CEEB;
        }
        
        .committee-amount .amount-value {
            color: #90EE90;
        }
        
        .worker-amount .amount-value {
            color: #FFD700;
        }
        
        .bottom-info {
            display: flex;
            gap: 5mm;
            margin: 5mm 0;
        }
        
        .info-box {
            background: #f8f9fa;
            padding: 3mm;
            border: 1px solid #000;
            text-align: center;
            font-size: 11px;
            flex: 1;
        }
        
        .info-box strong {
            display: block;
            margin-bottom: 2mm;
        }
        
        @media print {
            body {
                background-color: white;
                padding: 0;
                margin: 0;
            }
            
            .page-container {
                width: 100%;
                margin: 0;
            }
            
            .receipt-container {
                border: 2px solid #000;
                margin-bottom: 5mm;
                page-break-inside: avoid;
            }
            
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- الوصل الأول -->
        <div class="receipt-container">
            <!-- Header -->
            <div class="header">
                <!-- الشعار الأيسر -->
                <div class="logo"></div>
                
                <!-- العنوان الرئيسي مع رقم الوصل -->
                <div class="header-center">
                    <!-- رقم الوصل فوق العنوان -->
                    <div class="receipt-number">
                        الرقم: {{ receipt.receipt_number }}
                    </div>
                    <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
                    <h2>ولاية الجلفة - مديرية الإدارة المحلية</h2>
                    <h3>لجنة الخدمات الاجتماعية لموظفي الإدارة والولاية</h3>
                </div>
                
                <!-- الشعار الأيمن -->
                <div class="logo"></div>
            </div>
            
            <!-- عنوان الوصل -->
            <div class="receipt-title">
                <h2>وصل استلام ذات للحمام المعدني</h2>
                <div class="subtitle">حمام الشارف المبلغ: ({{ "{:,.0f}".format(receipt.total_amount) }} د.ج)</div>
            </div>
            
            <!-- معلومات الموظف -->
            <table class="info-table">
                <tr>
                    <td class="label">الاسم والنسب:</td>
                    <td>{{ receipt.worker.family_name }} {{ receipt.worker.given_name }}</td>
                    <td class="label">رقم الهاتف:</td>
                    <td>{{ receipt.worker.phone_number or 'غير محدد' }}</td>
                </tr>
                <tr>
                    <td class="label">التعيين:</td>
                    <td>{{ receipt.worker.personal_title or 'غير محدد' }}</td>
                    <td class="label">الفئة:</td>
                    <td>{{ receipt.worker.category or 'غير محدد' }}</td>
                </tr>
            </table>
            
            <!-- تفاصيل المبالغ -->
            <div class="amount-section">
                <h3>تفاصيل المبالغ</h3>
                <div class="amount-grid">
                    <div class="amount-item total-amount">
                        <span class="amount-label">المبلغ الإجمالي</span>
                        <div class="amount-value">{{ "{:,.0f}".format(receipt.total_amount) }} د.ج</div>
                    </div>
                    <div class="amount-item committee-amount">
                        <span class="amount-label">مساهمة اللجنة</span>
                        <div class="amount-value">{{ "{:,.0f}".format(receipt.committee_contribution) }} د.ج</div>
                    </div>
                    <div class="amount-item worker-amount">
                        <span class="amount-label">دفع العامل</span>
                        <div class="amount-value">{{ "{:,.0f}".format(receipt.worker_payment) }} د.ج</div>
                    </div>
                </div>
            </div>
            
            <!-- معلومات إضافية -->
            <div class="bottom-info">
                <div class="info-box">
                    <strong>مكان العلاج:</strong>
                    {{ receipt.treatment_location }}
                </div>
            </div>
        </div>
        
        <!-- الوصل الثاني (نسخة مطابقة) -->
        <div class="receipt-container">
            <!-- Header -->
            <div class="header">
                <!-- الشعار الأيسر -->
                <div class="logo"></div>
                
                <!-- العنوان الرئيسي مع رقم الوصل -->
                <div class="header-center">
                    <!-- رقم الوصل فوق العنوان -->
                    <div class="receipt-number">
                        الرقم: {{ receipt.receipt_number }}
                    </div>
                    <h1>الجمهورية الجزائرية الديمقراطية الشعبية</h1>
                    <h2>ولاية الجلفة - مديرية الإدارة المحلية</h2>
                    <h3>لجنة الخدمات الاجتماعية لموظفي الإدارة والولاية</h3>
                </div>
                
                <!-- الشعار الأيمن -->
                <div class="logo"></div>
            </div>
            
            <!-- عنوان الوصل -->
            <div class="receipt-title">
                <h2>وصل استلام ذات للحمام المعدني</h2>
                <div class="subtitle">حمام الشارف المبلغ: ({{ "{:,.0f}".format(receipt.total_amount) }} د.ج)</div>
            </div>
            
            <!-- معلومات الموظف -->
            <table class="info-table">
                <tr>
                    <td class="label">الاسم والنسب:</td>
                    <td>{{ receipt.worker.family_name }} {{ receipt.worker.given_name }}</td>
                    <td class="label">رقم الهاتف:</td>
                    <td>{{ receipt.worker.phone_number or 'غير محدد' }}</td>
                </tr>
                <tr>
                    <td class="label">التعيين:</td>
                    <td>{{ receipt.worker.personal_title or 'غير محدد' }}</td>
                    <td class="label">الفئة:</td>
                    <td>{{ receipt.worker.category or 'غير محدد' }}</td>
                </tr>
            </table>
            
            <!-- تفاصيل المبالغ -->
            <div class="amount-section">
                <h3>تفاصيل المبالغ</h3>
                <div class="amount-grid">
                    <div class="amount-item total-amount">
                        <span class="amount-label">المبلغ الإجمالي</span>
                        <div class="amount-value">{{ "{:,.0f}".format(receipt.total_amount) }} د.ج</div>
                    </div>
                    <div class="amount-item committee-amount">
                        <span class="amount-label">مساهمة اللجنة</span>
                        <div class="amount-value">{{ "{:,.0f}".format(receipt.committee_contribution) }} د.ج</div>
                    </div>
                    <div class="amount-item worker-amount">
                        <span class="amount-label">دفع العامل</span>
                        <div class="amount-value">{{ "{:,.0f}".format(receipt.worker_payment) }} د.ج</div>
                    </div>
                </div>
            </div>
            
            <!-- معلومات إضافية -->
            <div class="bottom-info">
                <div class="info-box">
                    <strong>مكان العلاج:</strong>
                    {{ receipt.treatment_location }}
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الطباعة والعودة -->
    <div style="text-align: center; margin-top: 20px;" class="no-print">
        <button onclick="window.print()" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px; cursor: pointer;">
            طباعة الوصل
        </button>
        <a href="{{ url_for('main.thermal_receipts_list') }}" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block;">
            العودة للقائمة
        </a>
    </div>
</body>
</html>
