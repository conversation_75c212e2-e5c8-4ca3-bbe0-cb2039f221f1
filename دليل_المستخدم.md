# دليل المستخدم - نظام إدارة لجنة الخدمات الاجتماعية

## مرحباً بكم في نظام إدارة لجنة الخدمات الاجتماعية لولاية الجلفة

هذا الدليل سيساعدكم على استخدام النظام بكفاءة وسهولة.

---

## 🚀 بدء التشغيل

### الطريقة الأولى (الأسهل):
1. انقر نقراً مزدوجاً على ملف `start_app.bat`
2. انتظر حتى يظهر "يمكنك الوصول للتطبيق عبر: http://localhost:5000"
3. افتح المتصفح وانتقل إلى: `http://localhost:5000`

### الطريقة الثانية (يدوية):
```bash
python run.py
```

---

## 📋 الوظائف الأساسية

### 1. الصفحة الرئيسية
- **عرض الإحصائيات**: إجمالي العمال، الأطفال، المتزوجين
- **الإجراءات السريعة**: إضافة عامل جديد، البحث
- **آخر العمال المضافين**: عرض أحدث 5 عمال

### 2. إضافة عامل جديد
#### البيانات الشخصية:
- ✅ الاسم واللقب (مطلوب)
- ✅ تاريخ ومكان الميلاد (مطلوب)
- ✅ الجنس: ذكر/أنثى (مطلوب)
- ✅ الحالة الاجتماعية: متزوج/عازب/مطلق/أرمل (مطلوب)

#### بيانات العمل:
- ✅ الوظيفة والمنصب (مطلوب)
- ✅ مكان العمل (مطلوب)
- ✅ صورة العامل (اختياري)

#### بيانات الأطفال:
- ✅ اسم الطفل وتاريخ الميلاد
- ✅ الجنس: ذكر/أنثى
- ✅ المستوى الدراسي: ابتدائي/متوسط/ثانوي/جامعي

### 3. قائمة العمال
- **عرض جميع العمال** مع بياناتهم الأساسية
- **البحث** بالاسم أو الوظيفة أو مكان العمل
- **عرض عدد الأطفال** لكل عامل
- **الإجراءات**: عرض التفاصيل، تعديل، حذف

### 4. تفاصيل العامل
- **البيانات الشخصية** كاملة
- **بيانات العمل** التفصيلية
- **قائمة الأطفال** مع أعمارهم ومستوياتهم الدراسية
- **إحصائيات سريعة**: عدد الأطفال، الذكور، الإناث، الجامعيين

### 5. تعديل بيانات العامل
- تعديل جميع البيانات الشخصية والوظيفية
- إضافة أو حذف أو تعديل بيانات الأطفال
- تغيير صورة العامل

### 6. حذف العامل
- تأكيد قبل الحذف
- حذف جميع البيانات المرتبطة (العامل والأطفال)

---

## 🔍 نصائح للاستخدام

### البحث الفعال:
- يمكن البحث بالاسم الأول أو اللقب
- البحث بالوظيفة أو مكان العمل
- البحث غير حساس لحالة الأحرف

### إدارة الصور:
- الصور المدعومة: JPG, PNG, GIF
- يتم تصغير الصور تلقائياً
- الصور اختيارية ولا تؤثر على حفظ البيانات

### إدارة الأطفال:
- يمكن إضافة عدد غير محدود من الأطفال
- يمكن حذف أي طفل بالنقر على زر الحذف
- جميع بيانات الأطفال مطلوبة

---

## ⚠️ تنبيهات مهمة

### الأمان:
- **لا تشارك رابط النظام** مع أشخاص غير مخولين
- **احتفظ بنسخة احتياطية** من قاعدة البيانات بانتظام
- **أغلق النظام** عند الانتهاء من الاستخدام

### النسخ الاحتياطي:
- ملف قاعدة البيانات: `instance/workers_committee.db`
- انسخ هذا الملف بانتظام لمكان آمن
- يمكن استرجاع البيانات بنسخ الملف مرة أخرى

### الصيانة:
- أعد تشغيل النظام إذا واجهت مشاكل
- تأكد من وجود مساحة كافية على القرص الصلب
- احذف الصور غير المستخدمة من مجلد `app/static/uploads`

---

## 🆘 حل المشاكل الشائعة

### المشكلة: لا يمكن الوصول للنظام
**الحل**: 
1. تأكد من تشغيل النظام بنجاح
2. تحقق من الرابط: `http://localhost:5000`
3. جرب إغلاق وإعادة تشغيل النظام

### المشكلة: لا تظهر الصور
**الحل**:
1. تأكد من أن الصورة بصيغة مدعومة (JPG, PNG, GIF)
2. تحقق من حجم الصورة (يفضل أقل من 5 ميجابايت)
3. جرب رفع صورة أخرى

### المشكلة: خطأ عند حفظ البيانات
**الحل**:
1. تأكد من ملء جميع الحقول المطلوبة
2. تحقق من صحة التواريخ
3. أعد تحميل الصفحة وحاول مرة أخرى

### المشكلة: البحث لا يعمل
**الحل**:
1. تأكد من كتابة النص بشكل صحيح
2. جرب البحث بكلمة واحدة فقط
3. تحقق من وجود بيانات في النظام

---

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع هذا الدليل أولاً
- تحقق من ملف `README.md` للمعلومات التقنية
- تواصل مع فريق التطوير

---

## 📊 البيانات التجريبية

تم إضافة 5 عمال تجريبيين مع 8 أطفال للاختبار:
1. **أحمد بن علي** - مهندس مدني (طفلان)
2. **خديجة بن سالم** - معلمة (طفل واحد)
3. **عبد الرحمن قاسمي** - طبيب (عازب)
4. **زينب العربي** - ممرضة (3 أطفال)
5. **مصطفى بوعلام** - مدير إداري (طفلان)

يمكنك حذف هذه البيانات وإضافة بياناتك الحقيقية.

---

**نتمنى لكم استخداماً موفقاً للنظام! 🌟**
