{% extends "base.html" %}

{% block title %}
إضافة بنك جديد - لجنة الخدمات الاجتماعية
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>إضافة بنك جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.bank_name.label(class="form-label") }}
                        {{ form.bank_name(class="form-control", placeholder="أدخل اسم البنك...") }}
                        {% if form.bank_name.errors %}
                            <div class="text-danger">
                                {% for error in form.bank_name.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="text-muted">
                            تأكد من صحة اسم البنك. سيظهر في قائمة البنوك عند إضافة/تعديل العمال.
                        </small>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('main.banks_list') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>إلغاء
                        </a>
                        {{ form.submit(class="btn btn-success") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- البنوك الموجودة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>البنوك الموجودة حالياً
                </h6>
            </div>
            <div class="card-body">
                {% set existing_banks = [] %}
                {% for bank in banks %}
                    {% if existing_banks.append(bank.name) %}{% endif %}
                {% endfor %}
                
                {% if existing_banks %}
                    <div class="row">
                        {% for bank_name in existing_banks %}
                        <div class="col-md-4 mb-2">
                            <span class="badge bg-light text-dark border">
                                <i class="fas fa-university me-1"></i>{{ bank_name }}
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted mb-0">لا توجد بنوك مضافة بعد</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نصائح -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h6 class="alert-heading">
                <i class="fas fa-lightbulb me-2"></i>نصائح مفيدة:
            </h6>
            <ul class="mb-0">
                <li>تأكد من كتابة اسم البنك بشكل صحيح ومطابق للاسم الرسمي</li>
                <li>تجنب إضافة بنوك مكررة - سيتم رفض الإضافة إذا كان البنك موجوداً</li>
                <li>يمكنك إدارة البنوك (تفعيل/إلغاء تفعيل/حذف) من صفحة قائمة البنوك</li>
                <li>البنوك المضافة ستظهر تلقائياً في قوائم إضافة وتعديل العمال</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}
