from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import StringField, DateField, SelectField, FieldList, FormField, SubmitField
from wtforms.validators import DataRequired, Length
from wtforms.widgets import TextArea

class ChildForm(FlaskForm):
    """نموذج بيانات الطفل"""
    name = StringField('اسم الطفل', validators=[Length(min=2, max=100)])
    birth_date = DateField('تاريخ الميلاد')
    gender = SelectField('الجنس',
                        choices=[('', 'اختر...'), ('ذكر', 'ذكر'), ('أنثى', 'أنثى')])
    education_level = SelectField('المستوى الدراسي',
                                 choices=[
                                     ('', 'اختر...'),
                                     ('ابتدائي', 'ابتدائي'),
                                     ('متوسط', 'متوسط'),
                                     ('ثانوي', 'ثانوي'),
                                     ('جامعي', 'جامعي')
                                 ])

class WorkerForm(FlaskForm):
    """نموذج بيانات العامل"""
    first_name = StringField('الاسم', validators=[DataRequired(), Length(min=2, max=100)])
    last_name = StringField('اللقب', validators=[DataRequired(), Length(min=2, max=100)])
    birth_date = DateField('تاريخ الميلاد', validators=[DataRequired()])
    birth_place = StringField('مكان الميلاد', validators=[DataRequired(), Length(min=2, max=200)])
    personal_address = StringField('العنوان الشخصي', validators=[DataRequired(), Length(min=5, max=300)])
    phone = StringField('الهاتف', validators=[DataRequired(), Length(min=10, max=15)])
    position = StringField('المنصب', validators=[DataRequired(), Length(min=2, max=200)])
    workplace = StringField('مكان العمل', validators=[DataRequired(), Length(min=2, max=300)])
    gender = SelectField('الجنس', 
                        choices=[('ذكر', 'ذكر'), ('أنثى', 'أنثى')], 
                        validators=[DataRequired()])
    marital_status = SelectField('الحالة الاجتماعية',
                                choices=[
                                    ('متزوج', 'متزوج'),
                                    ('عازب', 'عازب'),
                                    ('مطلق', 'مطلق'),
                                    ('أرمل', 'أرمل')
                                ],
                                validators=[DataRequired()])

    # معلومات الحساب البنكي/البريدي
    account_type = SelectField('نوع الحساب',
                              choices=[
                                  ('', 'اختر نوع الحساب...'),
                                  ('بنكي', 'حساب بنكي'),
                                  ('بريدي', 'حساب بريدي')
                              ])
    account_number = StringField('رقم الحساب (20 رقم)',
                                validators=[Length(min=20, max=20, message='يجب أن يكون رقم الحساب مكون من 20 رقم بالضبط')])
    bank_name = SelectField('اسم البنك',
                           choices=[
                               ('', 'اختر البنك...'),
                               ('بنك الفلاحة والتنمية الريفية', 'بنك الفلاحة والتنمية الريفية'),
                               ('بنك التنمية المحلية', 'بنك التنمية المحلية'),
                               ('البنك الوطني الجزائري', 'البنك الوطني الجزائري'),
                               ('القرض الشعبي الجزائري', 'القرض الشعبي الجزائري'),
                               ('بنك التوفير والاحتياط', 'بنك التوفير والاحتياط'),
                               ('البنك الخارجي الجزائري', 'البنك الخارجي الجزائري'),
                               ('بنك الخليج', 'بنك الخليج'),
                               ('خزينة الولاية', 'خزينة الولاية'),
                               ('أخرى', 'أخرى')
                           ])
    bank_agency = StringField('الوكالة', validators=[Length(max=100)])

    photo = FileField('صورة العامل', validators=[FileAllowed(['jpg', 'png', 'jpeg'], 'الصور فقط!')])

    # قائمة الأطفال
    children = FieldList(FormField(ChildForm), min_entries=0)

    submit = SubmitField('حفظ')
