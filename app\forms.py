from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import <PERSON>Field, DateField, SelectField, FieldList, FormField, SubmitField
from wtforms.validators import DataRequired, Length
from wtforms.widgets import TextArea

class ChildForm(FlaskForm):
    """نموذج بيانات الطفل"""
    name = StringField('اسم الطفل', validators=[DataRequired(), Length(min=2, max=100)])
    birth_date = DateField('تاريخ الميلاد', validators=[DataRequired()])
    gender = SelectField('الجنس', 
                        choices=[('ذكر', 'ذكر'), ('أنثى', 'أنثى')], 
                        validators=[DataRequired()])
    education_level = SelectField('المستوى الدراسي',
                                 choices=[
                                     ('ابتدائي', 'ابتدائي'),
                                     ('متوسط', 'متوسط'),
                                     ('ثانوي', 'ثانوي'),
                                     ('جامعي', 'جامعي')
                                 ],
                                 validators=[DataRequired()])

class WorkerForm(FlaskForm):
    """نموذج بيانات العامل"""
    first_name = StringField('الاسم', validators=[DataRequired(), Length(min=2, max=100)])
    last_name = StringField('اللقب', validators=[DataRequired(), Length(min=2, max=100)])
    birth_date = DateField('تاريخ الميلاد', validators=[DataRequired()])
    birth_place = StringField('مكان الميلاد', validators=[DataRequired(), Length(min=2, max=200)])
    job_title = StringField('الوظيفة', validators=[DataRequired(), Length(min=2, max=200)])
    position = StringField('المنصب', validators=[DataRequired(), Length(min=2, max=200)])
    workplace = StringField('مكان العمل', validators=[DataRequired(), Length(min=2, max=300)])
    gender = SelectField('الجنس', 
                        choices=[('ذكر', 'ذكر'), ('أنثى', 'أنثى')], 
                        validators=[DataRequired()])
    marital_status = SelectField('الحالة الاجتماعية',
                                choices=[
                                    ('متزوج', 'متزوج'),
                                    ('عازب', 'عازب'),
                                    ('مطلق', 'مطلق'),
                                    ('أرمل', 'أرمل')
                                ],
                                validators=[DataRequired()])
    photo = FileField('صورة العامل', validators=[FileAllowed(['jpg', 'png', 'jpeg'], 'الصور فقط!')])
    
    # قائمة الأطفال
    children = FieldList(FormField(ChildForm), min_entries=0)
    
    submit = SubmitField('حفظ')
