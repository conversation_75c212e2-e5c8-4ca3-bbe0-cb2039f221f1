{% extends "base.html" %}

{% block title %}
إدارة البنوك - لجنة الخدمات الاجتماعية
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-university me-2"></i>إدارة البنوك
                    <span class="badge bg-primary ms-2">{{ banks|length }}</span>
                </h5>
                <div>
                    <a href="/" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>العودة للرئيسية
                    </a>
                    <a href="{{ url_for('main.add_bank') }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>إضافة بنك جديد
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if banks %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>اسم البنك</th>
                                <th>الحالة</th>
                                <th>تاريخ الإضافة</th>
                                <th>عدد العمال المستخدمين</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for bank in banks %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>
                                    <strong>{{ bank.name }}</strong>
                                </td>
                                <td>
                                    {% if bank.is_active %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>نشط
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-pause me-1"></i>غير نشط
                                        </span>
                                    {% endif %}
                                </td>
                                <td>{{ bank.created_at.strftime('%Y-%m-%d') if bank.created_at }}</td>
                                <td>
                                    {% set worker_count = workers.filter_by(bank_name=bank.name).count() if workers else 0 %}
                                    <span class="badge bg-info">{{ worker_count }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <!-- تفعيل/إلغاء تفعيل -->
                                        <form method="POST" action="{{ url_for('main.toggle_bank', id=bank.id) }}" class="d-inline">
                                            {% if bank.is_active %}
                                                <button type="submit" class="btn btn-sm btn-outline-warning" 
                                                        title="إلغاء التفعيل"
                                                        onclick="return confirm('هل أنت متأكد من إلغاء تفعيل هذا البنك؟')">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                            {% else %}
                                                <button type="submit" class="btn btn-sm btn-outline-success" 
                                                        title="تفعيل"
                                                        onclick="return confirm('هل أنت متأكد من تفعيل هذا البنك؟')">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            {% endif %}
                                        </form>
                                        
                                        <!-- حذف -->
                                        <form method="POST" action="{{ url_for('main.delete_bank', id=bank.id) }}" class="d-inline">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                    title="حذف"
                                                    onclick="return confirm('هل أنت متأكد من حذف هذا البنك؟\\nسيتم حذفه نهائياً!')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-university fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بنوك</h5>
                    <p class="text-muted">ابدأ بإضافة بنك جديد</p>
                    <a href="{{ url_for('main.add_bank') }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>إضافة بنك جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>معلومات مهمة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">إضافة بنك جديد:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>سيظهر في قائمة البنوك عند إضافة/تعديل العمال</li>
                            <li><i class="fas fa-check text-success me-2"></i>يمكن تفعيله أو إلغاء تفعيله حسب الحاجة</li>
                            <li><i class="fas fa-check text-success me-2"></i>لا يمكن حذفه إذا كان مستخدماً من قبل عمال</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">ملاحظات:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>البنوك غير النشطة لن تظهر في القوائم</li>
                            <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>تأكد من صحة اسم البنك قبل الإضافة</li>
                            <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>يمكن استخدام خيار "أخرى" للبنوك المؤقتة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
