from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class Worker(db.Model):
    """نموذج العامل"""
    __tablename__ = 'workers'
    
    id = db.Column(db.Integer, primary_key=True)
    first_name = db.Column(db.String(100), nullable=False, comment='الاسم')
    last_name = db.Column(db.String(100), nullable=False, comment='اللقب')
    birth_date = db.Column(db.Date, nullable=False, comment='تاريخ الميلاد')
    birth_place = db.Column(db.String(200), nullable=False, comment='مكان الميلاد')
    personal_address = db.Column(db.String(300), nullable=False, comment='العنوان الشخصي')
    phone = db.Column(db.String(15), nullable=False, comment='الهاتف')
    position = db.Column(db.String(200), nullable=False, comment='المنصب')
    workplace = db.Column(db.String(300), nullable=False, comment='مكان العمل')
    gender = db.Column(db.String(10), nullable=False, comment='الجنس')  # ذكر/أنثى
    marital_status = db.Column(db.String(20), nullable=False, comment='الحالة الاجتماعية')  # متزوج/عازب/مطلق/أرمل

    # معلومات الحساب البنكي/البريدي
    account_number = db.Column(db.String(20), comment='رقم الحساب البنكي/البريدي')
    account_type = db.Column(db.String(10), comment='نوع الحساب')  # بنكي/بريدي
    bank_name = db.Column(db.String(100), comment='اسم البنك')
    bank_agency = db.Column(db.String(100), comment='الوكالة')

    photo_filename = db.Column(db.String(255), comment='اسم ملف الصورة')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')
    
    # العلاقة مع الأطفال
    children = db.relationship('Child', backref='worker', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Worker {self.last_name} {self.first_name}>'
    
    @property
    def full_name(self):
        return f"{self.last_name} {self.first_name}"
    
    @property
    def children_count(self):
        return len(self.children)

class Child(db.Model):
    """نموذج الطفل"""
    __tablename__ = 'children'
    
    id = db.Column(db.Integer, primary_key=True)
    worker_id = db.Column(db.Integer, db.ForeignKey('workers.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False, comment='اسم الطفل')
    birth_date = db.Column(db.Date, nullable=False, comment='تاريخ ميلاد الطفل')
    gender = db.Column(db.String(10), nullable=False, comment='جنس الطفل')  # ذكر/أنثى
    education_level = db.Column(db.String(20), nullable=False, comment='المستوى الدراسي')  # ابتدائي/متوسط/ثانوي/جامعي
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')
    
    def __repr__(self):
        return f'<Child {self.name}>'

class Bank(db.Model):
    """نموذج البنوك"""
    __tablename__ = 'banks'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True, comment='اسم البنك')
    is_active = db.Column(db.Boolean, default=True, comment='نشط')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')

    def __repr__(self):
        return f'<Bank {self.name}>'
