{% extends "base.html" %}

{% block title %}قائمة العمال - لجنة الخدمات الاجتماعية{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>قائمة العمال
                </h5>
                <a href="{{ url_for('main.add_worker') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>إضافة عامل جديد
                </a>
            </div>
            <div class="card-body">
                <!-- البحث -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <form method="GET" action="{{ url_for('main.workers_list') }}">
                            <div class="input-group">
                                <input type="text" class="form-control" name="search" 
                                       placeholder="البحث بالاسم أو الوظيفة..." 
                                       value="{{ request.args.get('search', '') }}">
                                <button class="btn btn-outline-secondary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                {% if workers %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم الكامل</th>
                                <th>تاريخ الميلاد</th>
                                <th>الوظيفة</th>
                                <th>مكان العمل</th>
                                <th>الحالة الاجتماعية</th>
                                <th>عدد الأطفال</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for worker in workers %}
                            <tr>
                                <td>
                                    {% if worker.photo_filename %}
                                        <img src="{{ url_for('static', filename='uploads/' + worker.photo_filename) }}" 
                                             alt="صورة {{ worker.full_name }}" class="worker-photo">
                                    {% else %}
                                        <div class="worker-photo bg-secondary d-flex align-items-center justify-content-center">
                                            <i class="fas fa-user text-white"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>{{ worker.full_name }}</td>
                                <td>{{ worker.birth_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ worker.job_title }}</td>
                                <td>{{ worker.workplace }}</td>
                                <td>{{ worker.marital_status }}</td>
                                <td>
                                    <span class="badge bg-info">{{ worker.children_count }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('main.worker_detail', id=worker.id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('main.edit_worker', id=worker.id) }}" 
                                           class="btn btn-sm btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete({{ worker.id }}, '{{ worker.full_name }}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بيانات عمال</h5>
                    <p class="text-muted">ابدأ بإضافة عامل جديد</p>
                    <a href="{{ url_for('main.add_worker') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>إضافة عامل جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف العامل <strong id="workerName"></strong>؟</p>
                <p class="text-danger">سيتم حذف جميع بيانات العامل والأطفال المرتبطين به نهائياً.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">حذف نهائياً</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(workerId, workerName) {
    document.getElementById('workerName').textContent = workerName;
    document.getElementById('deleteForm').action = "{{ url_for('main.delete_worker', id=0) }}".replace('0', workerId);
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
