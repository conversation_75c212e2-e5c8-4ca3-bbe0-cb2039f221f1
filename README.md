# نظام إدارة لجنة الخدمات الاجتماعية - ولاية الجلفة

## نظرة عامة

نظام ويب شامل لإدارة بيانات العمال والخدمات الاجتماعية لولاية الجلفة، مطور بلغة Python باستخدام إطار العمل Flask.

## المميزات

### إدارة بيانات العمال
- ✅ إضافة وتعديل وحذف بيانات العمال
- ✅ حفظ البيانات الشخصية (الاسم، اللقب، تاريخ الميلاد، مكان الميلاد)
- ✅ تسجيل بيانات العمل (الوظيفة، المنصب، مكان العمل)
- ✅ تحديد الجنس والحالة الاجتماعية
- ✅ رفع وعرض صور العمال

### إدارة بيانات الأطفال
- ✅ إضافة بيانات الأطفال لكل عامل
- ✅ تسجيل (الاسم، تاريخ الميلاد، الجنس، المستوى الدراسي)
- ✅ المستويات الدراسية: ابتدائي، متوسط، ثانوي، جامعي

### الواجهة والتصميم
- ✅ واجهة باللغة العربية مع دعم RTL
- ✅ تصميم متجاوب يعمل على جميع الأجهزة
- ✅ استخدام Bootstrap 5 مع الخطوط العربية
- ✅ أيقونات Font Awesome

### قاعدة البيانات
- ✅ قاعدة بيانات SQLite قابلة للحفظ والاسترجاع
- ✅ نماذج بيانات محسنة مع العلاقات
- ✅ حفظ تلقائي للتغييرات

## متطلبات النظام

- Python 3.7 أو أحدث
- المكتبات المطلوبة (موجودة في requirements.txt)

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل التطبيق
```bash
python run.py
```

أو

```bash
python app.py
```

### 3. الوصول للتطبيق
افتح المتصفح وانتقل إلى: `http://localhost:5000`

## هيكل المشروع

```
sosiel/
├── app/
│   ├── __init__.py          # إعدادات التطبيق
│   ├── models.py            # نماذج قاعدة البيانات
│   ├── routes.py            # مسارات التطبيق
│   ├── forms.py             # نماذج الإدخال
│   ├── static/
│   │   ├── css/
│   │   │   └── style.css    # ملف التنسيقات
│   │   └── uploads/         # مجلد الصور
│   └── templates/
│       ├── base.html        # القالب الأساسي
│       ├── index.html       # الصفحة الرئيسية
│       ├── workers_list.html # قائمة العمال
│       ├── worker_form.html  # نموذج إضافة/تعديل العامل
│       └── worker_detail.html # تفاصيل العامل
├── instance/
│   └── workers_committee.db # قاعدة البيانات
├── requirements.txt         # المتطلبات
├── app.py                  # ملف التشغيل الرئيسي
├── run.py                  # ملف التشغيل البديل
└── README.md               # هذا الملف
```

## الاستخدام

### الصفحة الرئيسية
- عرض إحصائيات سريعة (عدد العمال، الأطفال، المتزوجين)
- روابط سريعة للوظائف الأساسية
- عرض آخر العمال المضافين

### إضافة عامل جديد
1. انقر على "إضافة عامل جديد"
2. املأ البيانات الشخصية والوظيفية
3. أضف صورة العامل (اختياري)
4. أضف بيانات الأطفال
5. انقر "حفظ"

### عرض وإدارة العمال
- عرض قائمة جميع العمال
- البحث بالاسم أو الوظيفة
- عرض تفاصيل كل عامل
- تعديل أو حذف البيانات

## المعلومات التقنية

### التقنيات المستخدمة
- **Backend**: Python Flask
- **Database**: SQLite مع SQLAlchemy
- **Frontend**: HTML5, CSS3, JavaScript
- **UI Framework**: Bootstrap 5 RTL
- **Icons**: Font Awesome 6
- **Fonts**: Google Fonts (Cairo)

### الأمان
- حماية CSRF للنماذج
- تشفير كلمات المرور
- تحقق من أنواع الملفات المرفوعة
- تصغير الصور تلقائياً

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا المشروع مطور خصيصاً لولاية الجلفة - لجنة الخدمات الاجتماعية.

---

**تم التطوير بواسطة**: Augment Agent  
**التاريخ**: 2025-07-09  
**الإصدار**: 1.0.0
